import {useIsFocused, useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {
  Animated,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../../components/Sys';
import {formatVND} from '../../../services/util';
import {AccountSelectors} from '../../account/services/account.slice';
import {
  changeMode,
  changeScreenParent,
  fetchEquipmentStatusStart,
  fetchProducts,
  setCurrentItem,
  setCurrentTem,
  setDetailedViewModalVisible,
  setTemModalVisible,
} from '../../device-list/services/inventory.slice';
import {OwnerActions} from '../../owner/services/slice';
import {PosActions} from '../../sale-location/services/pos.slice';
import THEME from '../../../components/theme/theme';
import HomeKPISkeletonMenu from './HomeKPISkeletonMenu';

const HomeKPI = () => {
  const {width} = useWindowDimensions();
  const profile = useSelector(AccountSelectors.profile);
  const [ownerData, setOwnerData] = useState(undefined);
  const [ownerMonthData, setOwnerMonthData] = useState(undefined);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.95));
  const {navigate} = useNavigation();
  const dispatch = useDispatch();
  const {isPos, isStaff, isAgent, isBranchOwner, isBranchManager} = useSelector(
    state => state.account,
  );

  const isFocused = useIsFocused();
  const handleScanHeaderComplete = tem => {
    if (!tem?.id) {
      navigate('HomeScreen');
      return;
    }
    if (tem.item) {
      dispatch(setCurrentItem({...tem.item}));
      dispatch(changeMode(2));
      dispatch(setDetailedViewModalVisible({show: true}));
    } else {
      dispatch(setCurrentTem(tem));
      dispatch(setTemModalVisible(true));
    }
    navigate('HomeScreen');
  };

  const onPressMenuItem = x => {
    if (x.screen === 'SaleLocationScreen') {
      dispatch(PosActions.setPos([]));
      dispatch(
        PosActions.setBody({
          sort_order: 'asc',
          sort_by: 'last_checkin',
          page: 1,
        }),
      );
    }
    if (x.screen === 'VisitPlanScreen') {
      dispatch(PosActions.setPos([]));
      dispatch(
        PosActions.setBody({
          sort_order: 'asc',
          sort_by: 'last_checkin',
          pageSize: 10,
          page: 1,
          ...(isBranchOwner || isBranchManager ? {staff_owner: true} : {}),
          ...(isStaff && profile?.id ? {staff_id: profile.id} : {}),
          plan_month: moment().format('YYYY-MM'),
        }),
      );
    }
    navigate(x.screen, x.param ? x.param : {});
  };

  const menuList = [
    {
      name: 'Điểm bán',
      screen: 'SaleLocationScreen',
      icon: (
        <MaterialCommunityIcons name="map-marker" size={24} color="white" />
      ),
      gradient: ['#6366F1', '#8B5CF6'], // Soft purple to violet
      onPress: () => onPressMenuItem({screen: 'SaleLocationScreen'}),
    },
    {
      name: 'Kế hoạch',
      screen: 'VisitPlanScreen',
      icon: (
        <MaterialCommunityIcons name="calendar-check" size={24} color="white" />
      ),
      gradient: ['#10B981', '#059669'], // Soft green
      onPress: () => onPressMenuItem({screen: 'VisitPlanScreen'}),
    },
    {
      name: 'Chấm công',
      screen: '',
      icon: (
        <MaterialCommunityIcons name="clock-check" size={24} color="white" />
      ),
      gradient: ['#3B82F6', '#1D4ED8'], // Soft blue
      onPress: () => onPressMenuItem({screen: 'AttendanceScreen'}),
    },
    {
      name: 'Quét mã',
      screen: 'ScanCodeScreen',
      param: {onComplete: handleScanHeaderComplete},
      icon: (
        <MaterialCommunityIcons name="qrcode-scan" size={24} color="white" />
      ),
      gradient: ['#F59E0B', '#D97706'], // Soft orange
      onPress: () =>
        onPressMenuItem({
          screen: 'ScanCodeScreen',
          param: {onComplete: handleScanHeaderComplete},
        }),
    },
  ];

  useEffect(() => {
    if (isFocused) {
      // Animate entrance
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]).start();

      dispatch(changeMode(2));
      dispatch(changeScreenParent('HomeScreen'));
      dispatch(fetchProducts());
      dispatch(fetchEquipmentStatusStart());
      dispatch(
        OwnerActions.getOwnerCurrentDay({
          onSuccess: rs => {
            if (rs?.data?.items.length > 0) {
              setOwnerData(rs?.data?.items[0]);
            }
          },
        }),
      );
      dispatch(
        OwnerActions.getOwnerCurrentMonth({
          onSuccess: rs => {
            if (rs?.data?.stats) {
              setOwnerMonthData(rs?.data?.stats);
            }
          },
        }),
      );
    } else {
      // Reset animation when not focused
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.95);
    }
  }, [isFocused, fadeAnim, scaleAnim]);

  const cal = (current, pre) => {
    try {
      if (pre === 0) {
        return '∞';
      }
      const temp = Number(((current - pre) / pre) * 100);

      if (isNaN(temp)) {
        return '--';
      }
      if (temp < 0) {
        return `${temp.toFixed(2)}`;
      } else {
        return `+${temp.toFixed(2)}`;
      }
    } catch (error) {
      return '';
    }
  };

  const compareList = [
    {
      title: 'Doanh thu ngày',
      percent: cal(ownerData?.total_revenue, ownerData?.previous_revenue),
      value: formatVND(ownerData?.total_revenue),
      compareWith: formatVND(ownerData?.previous_revenue),
      date: moment().subtract('days', 1).format('DD/MM/YYYY'),
      icon: 'cash-multiple',
      gradient: ['#6366F1', '#4F46E5'], // Soft indigo
    },
    {
      title: 'Doanh thu tháng',
      percent: cal(ownerMonthData?.revenue, ownerMonthData?.previous_revenue),
      value: formatVND(ownerMonthData?.revenue),
      compareWith: formatVND(ownerMonthData?.previous_revenue),
      date: moment().subtract('days', 1).format('MM/YYYY'),
      icon: 'chart-line',
      gradient: ['#10B981', '#059669'], // Soft emerald
    },
    {
      title: 'Số vé',
      percent: cal(
        ownerData?.net_tickets_sold,
        ownerData?.previous_net_tickets_sold,
      ),
      value: formatVND(ownerData?.net_tickets_sold),
      compareWith: formatVND(ownerData?.previous_net_tickets_sold),
      date: moment().subtract('days', 1).format('DD/MM/YYYY'),
      icon: 'ticket',
      gradient: ['#3B82F6', '#1E40AF'], // Soft blue
    },
    {
      title: 'DT/Vé',
      percent: cal(
        Number(ownerData?.total_revenue / ownerData?.net_tickets_sold).toFixed(
          0,
        ),
        Number(
          ownerData?.previous_revenue / ownerData?.previous_net_tickets_sold,
        ).toFixed(0),
      ),
      value: formatVND(
        Number(ownerData?.total_revenue / ownerData?.net_tickets_sold).toFixed(
          0,
        ),
      ),
      compareWith: formatVND(
        Number(
          ownerData?.previous_revenue / ownerData?.previous_net_tickets_sold,
        ).toFixed(0),
      ),
      date: moment().subtract('days', 1).format('DD/MM/YYYY'),
      icon: 'calculator',
      gradient: ['#F59E0B', '#D97706'], // Soft amber
    },
  ];

  // Modern KPI Card Component
  const KPICard = ({item, index}) => {
    const [cardScale] = useState(new Animated.Value(1));

    const handlePressIn = () => {
      Animated.spring(cardScale, {
        toValue: 0.95,
        useNativeDriver: true,
      }).start();
    };

    const handlePressOut = () => {
      Animated.spring(cardScale, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    };

    const isPositive = item?.percent > 0;
    const isNegative = item?.percent < 0;

    return (
      <Animated.View
        style={[
          modernStyles.kpiCard,
          {
            transform: [{scale: cardScale}],
          },
        ]}>
        <TouchableOpacity
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
          style={modernStyles.kpiCardTouchable}>
          <LinearGradient
            colors={item.gradient}
            style={modernStyles.kpiCardGradient}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 1}}>
            {/* Header with Icon */}
            <View style={modernStyles.kpiCardHeader}>
              <View style={modernStyles.kpiIconContainer}>
                <MaterialCommunityIcons
                  name={item.icon}
                  size={20}
                  color="white"
                />
              </View>
              <View style={modernStyles.kpiPercentContainer}>
                {item?.percent !== 0 && (
                  <>
                    <View
                      style={[
                        modernStyles.kpiPercentBadge,
                        {
                          backgroundColor: isPositive
                            ? 'rgba(34, 197, 94, 0.9)'
                            : isNegative
                            ? 'rgba(239, 68, 68, 0.9)'
                            : 'rgba(255, 255, 255, 0.2)',
                        },
                      ]}>
                      <Sys.Text style={modernStyles.kpiPercent}>
                        {item?.percent}%
                      </Sys.Text>
                      {isPositive ? (
                        <MaterialCommunityIcons
                          name="trending-up"
                          size={14}
                          color="white"
                          style={{marginLeft: 4}}
                        />
                      ) : isNegative ? (
                        <MaterialCommunityIcons
                          name="trending-down"
                          size={14}
                          color="white"
                          style={{marginLeft: 4}}
                        />
                      ) : null}
                    </View>
                  </>
                )}
              </View>
            </View>

            {/* Title and Date */}
            <View style={modernStyles.kpiCardContent}>
              <Sys.Text style={modernStyles.kpiTitle}>{item?.title}</Sys.Text>
              <Sys.Text style={modernStyles.kpiDate}>{item?.date}</Sys.Text>
            </View>

            {/* Main Value */}
            <View style={modernStyles.kpiValueContainer}>
              <Sys.Text style={modernStyles.kpiValue}>{item?.value}</Sys.Text>
            </View>

            {/* Comparison */}
            <View style={modernStyles.kpiCompareContainer}>
              <Sys.Text style={modernStyles.kpiCompareText}>
                So sánh với {item?.compareWith}
              </Sys.Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Modern Menu Item Component
  const ModernMenuItem = ({item, index, onPress}) => {
    const [itemScale] = useState(new Animated.Value(1));

    const handlePressIn = () => {
      Animated.spring(itemScale, {
        toValue: 0.9,
        useNativeDriver: true,
      }).start();
    };

    const handlePressOut = () => {
      Animated.spring(itemScale, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    };

    return (
      <Animated.View
        style={[
          modernStyles.menuItem,
          {
            transform: [{scale: itemScale}],
          },
        ]}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
          style={modernStyles.menuItemTouchable}>
          <LinearGradient
            colors={item.gradient}
            style={modernStyles.menuItemGradient}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 1}}>
            {item.icon}
          </LinearGradient>
          <Sys.Text style={modernStyles.menuItemText}>{item.name}</Sys.Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };
  if (!profile?.id) {
    return <HomeKPISkeletonMenu />;
  }

  return (
    <Animated.View
      style={[
        modernStyles.container,
        {
          opacity: fadeAnim,
          transform: [{scale: scaleAnim}],
        },
      ]}>
      {/* Modern Menu Section */}
      {(isStaff || isBranchOwner) && (
        <View style={[modernStyles.menuSection, {width: width - 20}]}>
          <LinearGradient
            colors={['#ffffff', '#f8f9fa']}
            style={modernStyles.menuContainer}>
            <View style={modernStyles.menuGrid}>
              {menuList.map((item, index) => {
                if (item.name === 'Chấm công' && !isStaff) {
                  return null;
                }
                return (
                  <ModernMenuItem
                    key={item.name}
                    item={item}
                    index={index}
                    onPress={
                      item.onPress || (() => navigate(item.screen, item.param))
                    }
                  />
                );
              })}
            </View>
          </LinearGradient>
        </View>
      )}

      {/* Modern KPI Cards Section */}
      {(isPos || isAgent) && (
        <View style={[modernStyles.kpiSection, {width: width - 20}]}>
          <View style={modernStyles.kpiHeader}>
            <MaterialCommunityIcons
              name="chart-box"
              size={24}
              color={THEME.Color.primary}
            />
            <Sys.Text style={modernStyles.kpiHeaderText}>Thống kê</Sys.Text>
          </View>
          <FlatList
            scrollEnabled={false}
            numColumns={2}
            keyExtractor={(item, index) => `kpi-${index}`}
            data={compareList}
            renderItem={({item, index}) => (
              <KPICard item={item} index={index} />
            )}
            contentContainerStyle={modernStyles.kpiGrid}
          />
        </View>
      )}
      {/*{isBranchManager && (*/}
      {/*  <View*/}
      {/*    style={[*/}
      {/*      {*/}
      {/*        width: width - 20,*/}
      {/*        borderRadius: 20,*/}
      {/*        marginHorizontal: 10,*/}
      {/*        marginTop: 10,*/}
      {/*        gap: 10,*/}
      {/*        flexDirection: 'row',*/}
      {/*      },*/}
      {/*    ]}>*/}
      {/*    <KPIBranchManagerYearQuarter />*/}
      {/*    <KPIBranchManagerYear />*/}
      {/*  </View>*/}
      {/*)}*/}
    </Animated.View>
  );
};

// Modern Styles
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Menu Section Styles
  menuSection: {
    marginHorizontal: 10,
    marginVertical: 8,
  },
  menuContainer: {
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.12,
    shadowRadius: 20,
    elevation: 8,
  },
  menuHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  menuHeaderText: {
    fontSize: 18,
    fontWeight: '700',
    color: THEME.Color.text,
    marginLeft: 12,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    letterSpacing: 0.5,
  },
  menuGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  menuItem: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  menuItemTouchable: {
    alignItems: 'center',
    width: '100%',
  },
  menuItemGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    marginBottom: 12,
  },
  menuItemText: {
    fontSize: 13,
    fontWeight: '600',
    color: THEME.Color.text,
    textAlign: 'center',
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    lineHeight: 18,
  },

  // KPI Section Styles
  kpiSection: {
    marginHorizontal: 10,
    marginVertical: 8,
  },
  kpiHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  kpiHeaderText: {
    fontSize: 18,
    fontWeight: '700',
    color: THEME.Color.text,
    marginLeft: 12,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    letterSpacing: 0.5,
  },
  kpiGrid: {
    paddingHorizontal: 4,
  },
  kpiCard: {
    flex: 1,
    margin: 6,
  },
  kpiCardTouchable: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  kpiCardGradient: {
    padding: 20,
    borderRadius: 20,
    minHeight: 160,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  kpiCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  kpiIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  kpiPercentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  kpiPercentBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  kpiPercent: {
    fontSize: 12,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  kpiCardContent: {
    marginBottom: 16,
  },
  kpiTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginBottom: 4,
  },
  kpiDate: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  kpiValueContainer: {
    marginBottom: 12,
  },
  kpiValue: {
    fontSize: 24,
    fontWeight: '800',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    letterSpacing: 0.5,
  },
  kpiCompareContainer: {
    marginTop: 'auto',
  },
  kpiCompareText: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    lineHeight: 16,
  },
});

// Legacy styles (keeping for compatibility)
const styles = StyleSheet.create({
  oneMenuName: {
    fontSize: 12,
    fontWeight: '500',
    lineHeight: 20,
    color: '#0062FF',
    marginTop: 4,
  },
  oneMenu: {flex: 1, alignItems: 'center', justifyContent: 'center'},
  flex1: {
    flex: 1,
  },
  topContainerMain: {
    flex: 1,
  },
  container: {
    marginBottom: 60,
  },
  top: {
    backgroundColor: '#001451',
    paddingHorizontal: 20,
  },
  topContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bottom: {
    backgroundColor: 'white',
    margin: 10,
    borderRadius: 20,
    zIndex: 9,
    padding: 10,
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bottomTitle: {
    lineHeight: 20,
    fontSize: 12,
    color: '#848484',
  },
  bottomContent: {
    color: '#001451',
    fontSize: 18,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  name: {
    lineHeight: 28,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    color: 'white',
    fontSize: 18,
  },
  infor: {
    fontSize: 12,
    color: 'white',
    lineHeight: 20,
  },
  avatar: {
    height: 65,
    width: 65,
    borderRadius: 50,
    marginRight: 20,
    backgroundColor: '#00000010',
  },
});

export default HomeKPI;
