{"name": "vietlottdms", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start --reset-cache", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/geolocation": "^3.3.0", "@react-native-community/netinfo": "^11.3.2", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/app": "^20.1.0", "@react-native-firebase/messaging": "^20.1.0", "@react-native-picker/picker": "^2.7.5", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/devtools": "^6.0.26", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^2.0.1", "@shopify/react-native-skia": "^1.1.0", "axios": "^1.6.2", "crypto-js": "^4.2.0", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-duration-format": "^2.3.2", "moment-timezone": "^0.5.45", "qs": "^6.12.1", "react": "18.2.0", "react-content-loader": "^7.0.0", "react-native": "0.73.0", "react-native-animatable": "^1.4.0", "react-native-bootsplash": "^5.2.2", "react-native-calendars": "^1.1286.0", "react-native-chart-kit": "^6.12.0", "react-native-date-picker": "^4.3.5", "react-native-device-info": "^10.13.2", "react-native-document-picker": "^9.3.0", "react-native-dotenv": "^3.4.11", "react-native-element-dropdown": "^2.12.0", "react-native-gesture-handler": "2.19.0", "react-native-get-location": "^4.0.1", "react-native-graph": "^1.1.0", "react-native-image-crop-picker": "^0.40.2", "react-native-image-resizer": "^1.4.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "1.11.3", "react-native-maps-directions": "^1.9.0", "react-native-modal": "^13.0.1", "react-native-month-year-picker": "^1.9.0", "react-native-permissions": "^4.1.5", "react-native-progress": "^5.0.1", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "3.8.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.7.4", "react-native-screens": "3.34.0", "react-native-svg": "^14.1.0", "react-native-swipe-list-view": "^3.2.9", "react-native-switch": "^1.5.1", "react-native-toast-message": "^2.2.0", "react-native-touch-id": "^4.4.1", "react-native-vector-icons": "^10.0.3", "react-native-vision-camera": "4.5.2", "react-native-webview": "^13.10.0", "react-redux": "^9.0.4", "redux-saga": "^1.2.3", "victory-native": "^37.0.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "^0.73.18", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}